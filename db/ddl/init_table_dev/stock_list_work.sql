CREATE TABLE stock_list_work (
    corp_cd VARCHAR(4) NOT NULL,
    data_date TIMESTAMP NOT NULL,
    center_code VARCHAR(4) NOT NULL,
    stock_group_code VARCHAR(4) NOT NULL,
    shipping_code VARCHAR(16) NOT NULL,
    product_cd VARCHAR(10) NOT NULL,
    first_day_stock_count NUMERIC(7,0),
    arrival_quantity NUMERIC(7,0),
    shipping_quantity NUMERIC(7,0),
    arrival_quantity_irregular NUMERIC(7,0),
    shipping_quantity_irregular NUMERIC(7,0),
    carryover_stock_count NUMERIC(7,0),
    PRIMARY KEY (corp_cd, data_date, center_code, stock_group_code, shipping_code, product_cd)
);
COMMENT ON TABLE stock_list_work IS '在庫表ワーク';
COMMENT ON COLUMN stock_list_work.corp_cd IS '会社コード';
COMMENT ON COLUMN stock_list_work.data_date IS 'データ発生日付';
COMMENT ON COLUMN stock_list_work.center_code IS 'センターコード';
COMMENT ON COLUMN stock_list_work.stock_group_code IS '在庫グループコード';
COMMENT ON COLUMN stock_list_work.shipping_code IS '出荷先コード';
COMMENT ON COLUMN stock_list_work.product_cd IS '商品コード';
COMMENT ON COLUMN stock_list_work.first_day_stock_count IS '日初在庫数量';
COMMENT ON COLUMN stock_list_work.arrival_quantity IS '入荷数量';
COMMENT ON COLUMN stock_list_work.shipping_quantity IS '出荷数量';
COMMENT ON COLUMN stock_list_work.arrival_quantity_irregular IS '入荷数量（イレギュラー）';
COMMENT ON COLUMN stock_list_work.shipping_quantity_irregular IS '出荷数量（イレギュラー）';
COMMENT ON COLUMN stock_list_work.carryover_stock_count IS '繰越在庫数量';

