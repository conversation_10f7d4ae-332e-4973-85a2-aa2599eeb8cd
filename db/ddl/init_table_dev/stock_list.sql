CREATE TABLE stock_list (
    corp_cd VARCHAR(4) NOT NULL,
    data_date TIMESTAMP NOT NULL,
    center_code VARCHAR(4) NOT NULL,
    stock_group_code VARCHAR(4) NOT NULL,
    shipping_code VARCHAR(16) NOT NULL,
    product_cd VARCHAR(10) NOT NULL,
    first_day_stock_count NUMERIC(7,0),
    arrival_quantity NUMERIC(7,0),
    shipping_quantity NUMERIC(7,0),
    arrival_quantity_irregular NUMERIC(7,0),
    shipping_quantity_irregular NUMERIC(7,0),
    carryover_stock_count NUMERIC(7,0),
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (corp_cd, data_date, center_code, stock_group_code, shipping_code, product_cd)
);
COMMENT ON TABLE stock_list IS '在庫表';
COMMENT ON COLUMN stock_list.corp_cd IS '会社コード';
COMMENT ON COLUMN stock_list.data_date IS 'データ発生日付';
COMMENT ON COLUMN stock_list.center_code IS 'センターコード';
COMMENT ON COLUMN stock_list.stock_group_code IS '在庫グループコード';
COMMENT ON COLUMN stock_list.shipping_code IS '出荷先コード';
COMMENT ON COLUMN stock_list.product_cd IS '商品コード';
COMMENT ON COLUMN stock_list.first_day_stock_count IS '日初在庫数量';
COMMENT ON COLUMN stock_list.arrival_quantity IS '入荷数量';
COMMENT ON COLUMN stock_list.shipping_quantity IS '出荷数量';
COMMENT ON COLUMN stock_list.arrival_quantity_irregular IS '入荷数量（イレギュラー）';
COMMENT ON COLUMN stock_list.shipping_quantity_irregular IS '出荷数量（イレギュラー）';
COMMENT ON COLUMN stock_list.carryover_stock_count IS '繰越在庫数量';
COMMENT ON COLUMN stock_list.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN stock_list.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN stock_list.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN stock_list.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN stock_list.d_version IS 'デ連バージョン';

