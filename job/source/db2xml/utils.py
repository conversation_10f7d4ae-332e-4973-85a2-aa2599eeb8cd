#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, Any, List, Union, Tuple
import types
import itertools


def escape_xml_chars(text: str) -> str:
    """
    XML特殊文字のエスケープ処理
    Args:
        text: エスケープ対象の文字列
    Returns:
        str: エスケープ処理後の文字列
    """
    if not isinstance(text, str):
        text = str(text)
    return (
        text.replace("&", "&amp;")
        .replace("<", "&lt;")
        .replace(">", "&gt;")
        .replace('"', "&quot;")
        .replace("'", "&apos;")
    )


def convert_to_dict(record: Any, column_names: List[str] = None) -> Dict[str, Any]:
    """
    レコードをディクショナリに変換するヘルパーメソッド
    Args:
        record: 変換対象のレコード
    Returns:
        Dict: 変換後のディクショナリ
    """
    tee_type = type(itertools.tee([])[0])
    if hasattr(record, "_asdict"):
        return record._asdict()
    elif hasattr(record, "_mapping"):
        return dict(record._mapping)
    elif isinstance(record, (tuple, list, types.GeneratorType, tee_type)):
        if not column_names:
            # カラム名が未指定の場合はインデックスをキーとして使用
            return {str(i): v for i, v in enumerate(record)}

        # カラム名とデータを対応付け
        result = {}
        for i, value in enumerate(record):
            if i < len(column_names):
                result[column_names[i]] = value
        return result
    return record if isinstance(record, dict) else {}

def is_empty_iterable(it):
    try:
        next(it)
    except StopIteration:
        return True
    else:
        return False