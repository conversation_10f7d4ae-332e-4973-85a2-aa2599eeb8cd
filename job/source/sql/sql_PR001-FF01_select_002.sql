WITH combined_products AS (
    SELECT
      '0000' AS shop_code,
      pl.MAIL_ORDER_PRODUCT_CD AS sku_code,
      pl.MAIL_ORDER_PRODUCT_CD AS commodity_code,
      CASE
      WHEN (pl.PRODUCT_TYPE != '01' AND pl.PRODUCT_TYPE != '82') OR
                pl.SET_PRODUCT_FLG = '1' THEN 0
      ELSE ppl.TAX_EXC
      END AS unit_price,
      pl.PRODUCT_TYPE,
      pl.SET_PRODUCT_FLG,
      '' AS discount_price,
      '' AS reservation_price,
      pl.JAN AS jan_code,
      '' AS standard_detail1_name,
      '' AS standard_detail2_name,
      pl.MDM_INTEGRATION_MANAGEMENT_CD AS mdm_management_code,
      pl.WAREHOUSE_MANAGEMENT_CD AS hinban_code,
      CASE
      WHEN pl.PRODUCT_TYPE IN ('01','11')
      THEN '01'
      WHEN pl.PRODUCT_TYPE = '05' THEN '03'
      WHEN pl.PRODUCT_TYPE IN ('02','07') THEN '05'
      WHEN pl.PRODUCT_TYPE IN('81','82') THEN '91'
      ELSE ''
      END AS hinban_kind,
      0 AS member_price_applied_flg,
      0 AS member_price_discount_rate,
      0 AS member_price,
      CASE
      WHEN pl.PRODUCT_TYPE = '05' THEN 1
      WHEN pl.AIRMAIL_RACK_YN = '1' THEN 0
      ELSE 1
      END AS air_transport_flg,
      CASE pl.PRODUCT_TYPE
      WHEN '05' THEN '01'
      ELSE '00'
      END AS commodity_prod_pack_type,
      CASE
      WHEN pl.PRODUCT_TYPE = '05' THEN '0'
      ELSE pl.DELIVERY_NOTICE_UNDISPLAY_FLG
      END AS delivery_note_no_disp_flg,
      CASE
      WHEN pl.PRODUCT_TYPE = '07'
      THEN pl.use_point_cnt
      ELSE NULL
      END AS reduction_point,
      0 AS stock_threshold,
      '' AS orm_rowid,
      '' AS created_user,
      '' AS created_datetime,
      '' AS updated_user,
      '' AS updated_datetime
    FROM mdm.product_linkage pl
    LEFT JOIN mdm.period_price_linkage ppl ON
      pl.mdm_integration_management_cd = ppl.mdm_integration_management_cd AND
      ppl.mdm_integration_management_cd_nk IS NOT NULL
    LEFT JOIN (
      select
      mdm_integration_management_cd,
      max(apply_start_date) as max_apply_start_date
      from
      mdm.period_price_linkage as ppl3
      where
      ppl3.mdm_integration_management_cd_nk IS NOT NULL and
      ppl3.apply_start_date <= :diff_base_timestamp
      group by mdm_integration_management_cd
    ) AS ppl2 ON
      ppl.mdm_integration_management_cd = ppl2.mdm_integration_management_cd AND
      ppl.apply_start_date = ppl2.max_apply_start_date
    WHERE
    (
      (
         pl.PMS_U_YMD > :sync_datetime
         AND pl.PMS_U_YMD <= :diff_base_timestamp
      )
      OR TO_CHAR(ppl.apply_start_date, 'YYYYMMDD') = TO_CHAR(:diff_base_timestamp, 'YYYYMMDD')
    )
    AND (
      pl.PRODUCT_TYPE  < '20'
      OR pl.PRODUCT_TYPE  > '29'
    )
    AND (
      pl.PERIOD_SET_SALES_CHANNEL_1 = '10'
      OR pl.PERIOD_SET_SALES_CHANNEL_2 = '10'
      OR pl.PERIOD_SET_SALES_CHANNEL_3 = '10'
    )
    AND pl.MAIL_ORDER_PRODUCT_CD IS NOT NULL
    UNION
    SELECT
      '0000' AS shop_code,
      pe.MAIL_ORDER_PRODUCT_CD AS sku_code,
      pe.MAIL_ORDER_PRODUCT_CD AS commodity_code,
      CASE
      WHEN (pe.PRODUCT_TYPE != '01' AND pe.PRODUCT_TYPE != '82') OR
                pe.SET_PRODUCT_FLG = '1' THEN 0
      ELSE ppl.TAX_EXC
      END AS unit_price,
      pe.PRODUCT_TYPE,
      pe.SET_PRODUCT_FLG,
      '' AS discount_price,
      '' AS reservation_price,
      pe.JAN AS jan_code,
      '' AS standard_detail1_name,
      '' AS standard_detail2_name,
      pe.MDM_INTEGRATION_MANAGEMENT_CD AS mdm_management_code,
      pe.WAREHOUSE_MANAGEMENT_CD AS hinban_code,
      CASE
      WHEN pe.PRODUCT_TYPE IN ('01','11')
      THEN '01'
      WHEN pe.PRODUCT_TYPE = '05' THEN '03'
      WHEN pe.PRODUCT_TYPE IN ('02','07') THEN '05'
      WHEN pe.PRODUCT_TYPE IN('81','82') THEN '91'
      ELSE ''
      END AS hinban_kind,
      0 AS member_price_applied_flg,
      0 AS member_price_discount_rate,
      0 AS member_price,
      CASE
      WHEN pe.PRODUCT_TYPE = '05' THEN 1
      WHEN pe.AIRMAIL_RACK_YN = '1' THEN 0
      ELSE 1
      END AS air_transport_flg,
      CASE pe.PRODUCT_TYPE
      WHEN '05' THEN '01'
      ELSE '00'
      END AS commodity_prod_pack_type,
      CASE
      WHEN pe.PRODUCT_TYPE = '05' THEN '0'
      ELSE pe.DELIVERY_NOTICE_UNDISPLAY_FLG      
      END AS delivery_note_no_disp_flg,
      CASE
      WHEN pe.PRODUCT_TYPE = '07'
      THEN pe.use_point_cnt
      ELSE NULL
      END AS reduction_point,
      0 AS stock_threshold,
      '' AS orm_rowid,
      '' AS created_user,
      '' AS created_datetime,
      '' AS updated_user,
      '' AS updated_datetime
    FROM mdm.product_edit pe
    LEFT JOIN mdm.period_price_linkage ppl ON
      pe.mdm_integration_management_cd = ppl.mdm_integration_management_cd AND
      ppl.mdm_integration_management_cd_nk IS NOT NULL
    LEFT JOIN (
      select
      mdm_integration_management_cd,
      max(apply_start_date) as max_apply_start_date
      from
      mdm.period_price_linkage as ppl3
      where
      ppl3.mdm_integration_management_cd_nk IS NOT NULL and
      ppl3.apply_start_date <= :diff_base_timestamp
      group by mdm_integration_management_cd
    ) AS ppl2 ON
      ppl.mdm_integration_management_cd = ppl2.mdm_integration_management_cd AND
      ppl.apply_start_date = ppl2.max_apply_start_date
    WHERE COMPOSITION_OMS_LINK_FLG = '1'
      AND NOT EXISTS (
        SELECT 1
        FROM mdm.product_linkage pl_inner
        WHERE pl_inner.mdm_integration_management_cd = pe.mdm_integration_management_cd
      )
      AND
      (
        (
          pe.PMS_U_YMD > :sync_datetime
          AND pe.PMS_U_YMD <= :diff_base_timestamp
        )
        OR TO_CHAR(ppl.apply_start_date, 'YYYYMMDD') = TO_CHAR(:diff_base_timestamp, 'YYYYMMDD')
      )
      AND (
        pe.PRODUCT_TYPE  < '20'
        OR pe.PRODUCT_TYPE  > '29'
      )
      AND (
        pe.PERIOD_SET_SALES_CHANNEL_1 = '10'
        OR pe.PERIOD_SET_SALES_CHANNEL_2 = '10'
        OR pe.PERIOD_SET_SALES_CHANNEL_3 = '10'
      )
      AND pe.MAIL_ORDER_PRODUCT_CD IS NOT NULL
)
SELECT
  a.shop_code,
  a.sku_code,
  a.commodity_code,
  a.unit_price,
  a.discount_price,
  a.reservation_price,
  a.jan_code,
  a.standard_detail1_name,
  a.standard_detail2_name,
  a.hinban_code,
  a.hinban_kind,
  a.member_price_applied_flg,
  a.member_price_discount_rate,
  a.member_price,
  a.air_transport_flg,
  a.commodity_prod_pack_type,
  a.delivery_note_no_disp_flg,
  a.reduction_point,
  a.stock_threshold,
  a.orm_rowid,
  a.created_user,
  a.created_datetime,
  a.updated_user,
  a.updated_datetime
FROM combined_products a
