# IF 設計書修正対応関係一覧

## 概要

redmine-8254 対応における断面差異対応で、MDM/OMS を基準としたデ連・DWHIF 設計書の修正対応関係を整理。

## エンティティ別修正対応関係

### 1. カテゴリ

**修正対象ファイル:**

- **デ連**: `IF-DW102-DF01_IF設計書_カテゴリ.xlsx`
- **DWH**: `IF-OMS-PR-001_IF設計書_カテゴリ.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| スマートフォン用カテゴリ名称 | 必須追加が必要 | デ連、DWH |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・必須に設定：スマートフォン用カテゴリ名称
```

### 2. クーポン/クーポンマスタ

**修正対象ファイル:**

- **デ連**: `IF-DW147-DF01_IF設計書_クーポンマスタ.xlsx`
- **DWH**: `IF-OMS-CO-001_IF設計書_クーポン.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| クーポン商品シリーズ | 項目削除が必要 | デ連、DWH |
| クーポン大分類 | 項目削除が必要 | デ連、DWH |
| クーポン自動発行種別 | 必須設定が必要 | デ連、DWH |
| クーポン部門 | 項目削除が必要 | デ連、DWH |
| 販売経路指定 | 項目削除が必要 | デ連、DWH |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・項目削除：クーポン商品シリーズ/クーポン大分類/クーポン部門/販売経路指定
 ・必須に設定：クーポン自動発行種別
```

### 3. 出荷ヘッダ

**修正対象ファイル:**

- **デ連**: `IF-DW138-DF01_IF設計書_出荷ヘッダ.xlsx`
- **DWH**: `IF-OMS-SP-002_IF設計書_出荷ヘッダ.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| 会員ステージ | 必須設定が必要 | デ連、DWH |
| 住所 3 | デ連 IF のみ桁数修正が必要 | デ連のみ |
| 佐川集荷日 | 項目追加が必要 | デ連、DWH |
| 受取店舗 ID | 項目追加が必要 | デ連、DWH |
| 受取店舗区分 | 項目追加が必要 | デ連、DWH |
| 受取店舗名称 | 項目追加が必要 | デ連、DWH |
| 宛名：名 | デ連 IF のみ必須解除が必要 | デ連のみ |
| 宛名名かな | デ連 IF のみ必須解除が必要 | デ連のみ |
| 送料消費税額 | 桁数修正が必要 | デ連、DWH |
| 配送情報番号 | 項目追加が必要 | デ連、DWH |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・項目追加：佐川集荷日/受取店舗ID/受取店舗区分/受取店舗名称/配送情報番号
 ・必須に設定：会員ステージ
 ・必須を解除：宛名：名/宛名名かな（デ連のみ）
 ・桁数修正：住所3（デ連のみ）/送料消費税額
```

### 4. 出荷明細構成品

**修正対象ファイル:**

- **デ連**: `IF-DW117-DF01_IF設計書_出荷明細構成品.xlsx`
- **DWH**: `IF-OMS-SP-003_IF設計書_出荷明細構成品.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| 商品名称 | デ連 IF のみ桁数修正が必要 | デ連のみ |
| 商品消費税額 | 桁数修正が必要 | デ連、DWH |
| 販売時消費税額 | 桁数修正が必要 | デ連、DWH |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・桁数修正：商品名称（デ連のみ）/商品消費税額/販売時消費税額
```

### 5. 受注キャンペーン適用

**修正対象ファイル:**

- **デ連**: `IF-DW119-DF01_IF設計書_受注キャンペーン適用.xlsx`
- **DWH**: `IF-OMS-CP-011_IF設計書_受注キャンペーン適用.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| キャンペーン適用終了日 | デ連 IF のみ桁数修正が必要? | デ連のみ |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・桁数修正：キャンペーン適用終了日（デ連のみ）
```

### 6. 受注キャンペーン適用履歴

**修正対象ファイル:**

- **デ連**: `IF-DW120-DF01_IF設計書_受注キャンペーン適用履歴.xlsx`
- **DWH**: `IF-OMS-CP-012_IF設計書_受注キャンペーン適用履歴.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| キャンペーン適用終了日 | デ連 IF のみ桁数修正が必要? | デ連のみ |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・桁数修正：キャンペーン適用終了日（デ連のみ）
```

### 7. 商品セグメントマスタ

**修正対象ファイル:**

- **デ連**: `IF-DW123-DF01_IF設計書_商品セグメントマスタ.xlsx`
- **DWH**: `IF-CDP-PR-004_IF設計書_商品セグメントマスタ.xlsx` / `IF-MDM-PR-007_IF設計書_商品セグメントマスタ.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| 更新クラス | 項目追加が必要 | デ連、DWH |
| 更新日 | 項目追加が必要 / 項目削除が必要 | デ連、DWH |
| 更新者 ID | 項目追加が必要 / 項目削除が必要 | デ連、DWH |
| 登録クラス | 項目追加が必要 | デ連、DWH |
| 登録日 | 項目追加が必要 / 項目削除が必要 | デ連、DWH |
| 登録者 ID | 項目追加が必要 / 項目削除が必要 | デ連、DWH |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・項目追加：更新クラス/登録クラス
 ・項目削除：更新日/更新者ID/登録日/登録者ID
```

### 8. 商品種別マスタ

**修正対象ファイル:**

- **デ連**: `IF-DW125-DF01_IF設計書_商品種別マスタ.xlsx`
- **DWH**: `IF-CDP-PR-009_IF設計書_商品種別マスタ.xlsx` / `IF-MDM-PR-013_IF設計書_商品種別マスタ.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| 更新クラス | 項目追加が必要 | デ連、DWH |
| 更新日 | 項目追加が必要 / 項目削除が必要 | デ連、DWH |
| 更新者 ID | 項目追加が必要 / 項目削除が必要 | デ連、DWH |
| 登録クラス | 項目追加が必要 | デ連、DWH |
| 登録日 | 項目追加が必要 / 項目削除が必要 | デ連、DWH |
| 登録者 ID | 項目追加が必要 / 項目削除が必要 | デ連、DWH |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・項目追加：更新クラス/登録クラス
 ・項目削除：更新日/更新者ID/登録日/登録者ID
```

### 9. 在庫

**修正対象ファイル:**

- **デ連**: `IF-DW126-DF01_IF設計書_在庫.xlsx`
- **DWH**: `IF-OMS-ST-001_IF設計書_在庫.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| 予約数量 | 必須設定が必要 | デ連、DWH |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・必須に設定：予約数量
```

### 10. 売上実績

**修正対象ファイル:**

- **デ連**: `IF-DW128-DF01_IF設計書_売上実績.xlsx`
- **DWH**: `IF-OMS-SL-001_IF設計書_売上実績.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| 入力番号 | 項目追加が必要 | デ連、DWH |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・項目追加：入力番号
```

### 11. 大分類マスタ

**修正対象ファイル:**

- **デ連**: `IF-DW129-DF01_IF設計書_大分類マスタ.xlsx`
- **DWH**: `IF-CDP-PR-010_IF設計書_大分類マスタ.xlsx` / `IF-MDM-PR-014_IF設計書_大分類マスタ.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| 更新クラス | 項目追加が必要 | デ連、DWH |
| 更新日 | 項目追加が必要 / 項目削除が必要 | デ連、DWH |
| 更新者 ID | 項目追加が必要 / 項目削除が必要 | デ連、DWH |
| 登録クラス | 項目追加が必要 | デ連、DWH |
| 登録日 | 項目追加が必要 / 項目削除が必要 | デ連、DWH |
| 登録者 ID | 項目追加が必要 / 項目削除が必要 | デ連、DWH |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・項目追加：更新クラス/登録クラス
 ・項目削除：更新日/更新者ID/登録日/登録者ID
```

### 12. 定期契約ヘッダ

**修正対象ファイル:**

- **デ連**: `IF-DW137-DF01_IF設計書_定期契約ヘッダ.xlsx`
- **DWH**: `IF-CDP-OR-003_IF設計書_定期契約ヘッダ.xlsx` / `IF-OMS-OR-004_IF設計書_定期契約ヘッダ.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| デバイス区分 | 項目追加が必要 | デ連、DWH |
| 外部受注番号 | デ連 IF のみ桁数修正が必要 | デ連のみ |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・項目追加：デバイス区分
 ・桁数修正：外部受注番号（デ連のみ）
```

### 13. 定期契約明細構成品

**修正対象ファイル:**

- **デ連**: `IF-DW130-DF01_IF設計書_定期契約明細構成品.xlsx`
- **DWH**: `IF-CDP-OR-005_IF設計書_定期契約明細構成品.xlsx` / `IF-OMS-OR-006_IF設計書_定期契約明細構成品.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| 商品名称 | デ連 IF のみ桁数修正が必要 | デ連のみ |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・桁数修正：商品名称（デ連のみ）
```

### 14. 期間別価格連携マスタ

**修正対象ファイル:**

- **デ連**: `IF-DW140-DF01_IF設計書_期間別価格連携マスタ.xlsx`
- **DWH**: `IF-CDP-PR-001_IF設計書_期間別価格連携マスタ.xlsx` / `IF-MDM-PR-003_IF設計書_期間別価格連携マスタ.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| 更新クラス | 項目追加が必要 | デ連、DWH |
| 更新日 | 項目追加が必要 / 項目削除が必要 | デ連、DWH |
| 更新者 ID | 項目追加が必要 / 項目削除が必要 | デ連、DWH |
| 登録クラス | 項目追加が必要 | デ連、DWH |
| 登録日 | 項目追加が必要 / 項目削除が必要 | デ連、DWH |
| 登録者 ID | 項目追加が必要 / 項目削除が必要 | デ連、DWH |
| 適用開始日 | デ連 IF のみ桁数修正が必要? | デ連のみ |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・項目追加：更新クラス/登録クラス
 ・項目削除：更新日/更新者ID/登録日/登録者ID
 ・桁数修正：適用開始日（デ連のみ）
```

### 15. 返品ヘッダ

**修正対象ファイル:**

- **デ連**: `IF-DW133-DF01_IF設計書_返品ヘッダ.xlsx`
- **DWH**: `IF-OMS-RT-001_IF設計書_返品ヘッダ.xlsx`

**修正内容:**
| 項目名 | 対応内容 | 影響範囲 |
|--------|----------|----------|
| キャンペーン返品額 | 項目追加が必要 | デ連、DWH |
| クーポン返品額 | 項目追加が必要 | デ連、DWH |
| キャンペーン判定基準日時 | 項目追加が必要 | デ連、DWH |
| 受注番号 | デ連 IF のみユニーク解除が必要 | デ連のみ |
| 返品前クーポン利用額 | 項目追加が必要 | デ連、DWH |
| 返品後クーポン利用額 | 項目追加が必要 | デ連、DWH |

**変更履歴記載内容:**

```
#8254対応
断面差異対応(IF)
 ・項目追加：キャンペーン返品額/クーポン返品額/キャンペーン判定基準日時/返品前クーポン利用額/返品後クーポン利用額
 ・ユニーク制約解除：受注番号（デ連のみ）
```

## 修正作業サマリー

### 修正対象ファイル数

- **デ連 IF 設計書**: 15 ファイル
- **DWHIF 設計書**: 20 ファイル（一部エンティティで複数ファイル）

### 対応内容別統計

- **項目追加が必要**: 25 項目
- **項目削除が必要**: 20 項目
- **必須設定が必要**: 4 項目
- **必須解除が必要**: 2 項目（デ連のみ）
- **桁数修正が必要**: 9 項目
- **ユニーク制約解除が必要**: 1 項目（デ連のみ）

### 作業優先度

1. **高優先度**: 必須属性の設定・解除（データ整合性に直接影響）
2. **中優先度**: 項目の追加・削除（機能に影響）
3. **低優先度**: 桁数修正、制約の調整（運用に影響）

## 注意事項

- 各 IF 設計書の変更履歴シートに統一形式で変更内容を記録
- 版番号は変更内容に応じて適切に更新（通常は 1.01 等のマイナーバージョンアップ）
- 担当者は「TIS 黄」で統一
- 変更日は実際の作業実施日を記録
