# IF 設計書調整ルール

## システム構成とデータフロー

```
MDM/OMS（上流） → デ連（中流） → DWH（下流）
```

**基本原則**: 上流システム（MDM/OMS）の項目仕様を基準として、下流システム（デ連・DWH）の IF 設計書を調整する

## 対応内容別調整ルール

### 1. 項目の存在に関する調整

#### 項目追加が必要

- **状況**: MDM/OMS に項目が存在するが、デ連・DWH の IF 設計書に項目が存在しない
- **対応**:
  - デ連 IF 設計書に項目を追加
  - DWHIF 設計書に項目を追加
- **影響範囲**: デ連、DWH 両方

#### 項目削除が必要

- **状況**: MDM/OMS に項目が存在しないが、デ連・DWH の IF 設計書に項目が存在する
- **対応**:
  - デ連 IF 設計書から項目を削除
  - DWHIF 設計書から項目を削除
- **影響範囲**: デ連、DWH 両方

### 2. 必須属性に関する調整

#### 必須追加が必要 / 必須設定が必要

- **状況**: MDM/OMS で必須項目だが、下流システムで必須設定されていない
- **対応**:
  - デ連 IF 設計書で必須属性を設定
  - DWHIF 設計書で必須属性を設定
- **影響範囲**: デ連、DWH 両方

#### デ連 IF のみ必須解除が必要

- **状況**: MDM/OMS で必須でないが、デ連 IF 設計書で必須設定されている
- **対応**:
  - デ連 IF 設計書の必須属性を解除
- **影響範囲**: デ連のみ

### 3. データ仕様に関する調整

#### 桁数修正が必要

- **状況**: MDM/OMS と下流システムで項目の桁数仕様が異なる
- **対応**:
  - デ連 IF 設計書の桁数を MDM/OMS に合わせて修正
  - DWHIF 設計書の桁数を MDM/OMS に合わせて修正
- **影響範囲**: デ連、DWH 両方

#### デ連 IF のみ桁数修正が必要

- **状況**: MDM/OMS とデ連 IF 設計書で項目の桁数仕様が異なる
- **対応**:
  - デ連 IF 設計書の桁数を MDM/OMS に合わせて修正
- **影響範囲**: デ連のみ

### 4. 制約に関する調整

#### デ連 IF のみユニーク解除が必要

- **状況**: MDM/OMS でユニーク制約がないが、デ連 IF 設計書でユニーク制約が設定されている
- **対応**:
  - デ連 IF 設計書のユニーク制約を解除
- **影響範囲**: デ連のみ

## 調整パターンの分類

### 全システム対象の調整

- 項目追加が必要
- 項目削除が必要
- 必須追加が必要 / 必須設定が必要
- 桁数修正が必要

### デ連のみ対象の調整

- デ連 IF のみ必須解除が必要
- デ連 IF のみ桁数修正が必要
- デ連 IF のみユニーク解除が必要

## 作業手順

1. **MDM/OMS 仕様の確認**: 基準となる上流システムの項目仕様を確認
2. **現状の IF 設計書確認**: デ連・DWH の現在の IF 設計書を確認
3. **差分の特定**: 上流と下流の仕様差分を特定
4. **調整方針の決定**: 上記ルールに基づいて調整方針を決定
5. **IF 設計書の修正**:
   - デ連 IF 設計書の修正
   - DWHIF 設計書の修正
6. **変更履歴の追加**: IF 設計書の変更履歴シートに変更内容を記録
7. **影響範囲の確認**: 修正による他システムへの影響を確認

## 変更履歴シート記録ルール

### 変更履歴の記録形式

| No. | 版   | 日付       | 区分 | 変更前 | 変更後 | 変更内容                                                                                                                                             | 担当者 |
| --- | ---- | ---------- | ---- | ------ | ------ | ---------------------------------------------------------------------------------------------------------------------------------------------------- | ------ |
| 4   | 1.01 | 2025/07/09 | 変更 | -      | -      | "#8254 対応<br>断面差異対応(IF)<br> ・項目削除：クーポン商品シリーズ/クーポン大分類/クーポン部門/販売経路指定<br> ・必須に設定:クーポン自動発行種別" | TIS 黄 |

### 変更内容の記載ルール

#### 項目追加の場合

```
#8254対応
断面差異対応(IF)
 ・項目追加：[項目名1]/[項目名2]/[項目名3]
```

#### 項目削除の場合

```
#8254対応
断面差異対応(IF)
 ・項目削除：[項目名1]/[項目名2]/[項目名3]
```

#### 必須設定の場合

```
#8254対応
断面差異対応(IF)
 ・必須に設定：[項目名1]/[項目名2]/[項目名3]
```

#### 必須解除の場合

```
#8254対応
断面差異対応(IF)
 ・必須を解除：[項目名1]/[項目名2]/[項目名3]
```

#### 桁数修正の場合

```
#8254対応
断面差異対応(IF)
 ・桁数修正：[項目名1]([変更前桁数]→[変更後桁数])/[項目名2]([変更前桁数]→[変更後桁数])
```

#### ユニーク制約解除の場合

```
#8254対応
断面差異対応(IF)
 ・ユニーク制約解除：[項目名1]/[項目名2]
```

#### 複数種類の変更がある場合

```
#8254対応
断面差異対応(IF)
 ・項目追加：[項目名1]/[項目名2]
 ・項目削除：[項目名3]/[項目名4]
 ・必須に設定：[項目名5]
 ・桁数修正：[項目名6]([変更前桁数]→[変更後桁数])
```

### 版番号の管理ルール

- **メジャーバージョン**: 大幅な仕様変更時（例：1.00 → 2.00）
- **マイナーバージョン**: 項目の追加・削除・属性変更時（例：1.00 → 1.01）
- **パッチバージョン**: 軽微な修正時（例：1.01 → 1.02）

### 変更履歴追加時の注意事項

1. **No.の連番**: 既存の最大 No.に+1 して設定
2. **版の更新**: 変更内容に応じて適切に版番号を更新
3. **日付**: 変更作業実施日を記録
4. **区分**: 通常は「変更」を設定
5. **変更前/変更後**: 項目レベルの変更では通常「-」を設定
6. **変更内容**: 上記ルールに従って統一形式で記載
7. **担当者**: 実際の作業担当者名を記録

## 注意事項

- **上流基準の原則**: 常に MDM/OMS の仕様を基準とする
- **データ整合性**: 修正により上流から下流へのデータフローが正常に動作することを確認
- **既存データへの影響**: 既存データに対する影響を事前に評価
- **段階的な適用**: 必要に応じて段階的に IF 設計書を修正
- **テスト計画**: 修正後のテスト計画を事前に策定

## 対応優先度

1. **高優先度**: 必須属性の追加・削除（データ整合性に直接影響）
2. **中優先度**: 項目の追加・削除（機能に影響）
3. **低優先度**: 桁数修正、制約の調整（運用に影響）

すべての調整は担当者「黄」により実施される。

## Excel ファイル操作ルール

### 基本ルール

- **Excel ツール使用必須**: Excel ファイルの読み取り・書き込み・修正には必ず Excel ツールを使用する
- **xlsx-to-markdown 禁止**: Excel ファイルの内容確認に xlsx-to-markdown ツールは使用しない

### IF ファイル定義シート修正ルール

#### ⚠️ 重要：シート構造の理解（操作ミス防止）

**IF ファイル定義シートは項目毎に 2 行構造になっている**

```
行18: ①ヘッダーレコード
行19-35: ヘッダーレコード部分（項番1-17）
行36: ②データレコード
行37-53: データレコード部分（項番1-17）
```

**例：スマートフォン用カテゴリ名称（項番 3）**

- **ヘッダーレコード部分**: 行 21（項番 3）
- **データレコード部分**: 行 39（項番 3）← **修正対象はこちら**

#### ⚠️ 操作ミス防止の重要ポイント

1. **修正対象の確認**

   - **必須・一意属性の修正**: 必ず「② データレコード」部分（行 37 以降）で実施
   - **ヘッダーレコード部分（行 19-35）は修正しない**

2. **行番号の確認方法**

   ```
   1. 「②データレコード」の見出し（行36）を確認
   2. その後の項番で対象項目を特定
   3. 項番3なら行39、項番5なら行41
   ```

3. **列位置の動的確認**
   - P 列：一意、Q 列：必須（デ連側の一般的な位置）
   - ファイルによって異なる可能性があるため、必ずヘッダー行で確認

#### 🔍 修正前の必須確認手順（操作ミス防止）

**Step 1: ファイル構造の確認**

```
1. read_data_from_excel でヘッダー行（行16）を確認
2. P列「一意」、Q列「必須」の位置を確認
3. 「②データレコード」見出し（行36）の存在を確認
```

**Step 2: 対象項目の特定**

```
1. 修正対象項目名を確認（例：スマートフォン用カテゴリ名称）
2. データレコード部分（行37以降）で項目名を検索
3. 該当する項番と行番号を特定（例：項番3 = 行39）
```

**Step 3: 修正前の値確認**

```
1. 特定した行の必須列（Q列）の現在値を確認
2. 修正内容と一致するか確認（例：「-」→「○」）
```

#### 修正ルール

- **⚠️ 重要**: 必ず「② データレコード」部分（行 37 以降）で修正する
- **改修箇所のマーキング**: 修正した項目は背景色を赤色でマークする
- **必須属性変更**: データレコード部分の必須列で「○」「-」を適切に変更し、赤色でマーク
- **一意属性変更**: データレコード部分の一意列で「○」「-」を適切に変更し、赤色でマーク
- **桁数変更**: 桁数列の数値を変更し、赤色でマーク
- **項目追加**:
  - ヘッダーレコード部分に新規行を追加し、全セルを赤色でマーク
  - データレコード部分に新規行を追加し、全セルを赤色でマーク
- **項目削除**:
  - ヘッダーレコード部分の該当行を削除（赤色マークは不要）
  - データレコード部分の該当行を削除（赤色マークは不要）

### 変更履歴シート更新ルール

- **No. の連番**: 既存の最大 No.に+1
- **版番号**: 適切にインクリメント（通常マイナーバージョンアップ）
- **変更内容**: 統一形式で記載
- **担当者**: 「TIS 黄」で統一

## フォーマット相違対応ルール

### デ連側と DWH 側のフォーマット相違

#### 基本的な相違点

- **デ連側**：必須列は Q 列に位置することが多い
- **DWH 側**：必須列は S 列に位置することが多い
- **重要**: 固定の列番号に依存せず、動的に列位置を確認する

#### 動的な列関係確認ルール

ファイルフォーマットが異なる可能性があるため、以下の手順で動的に列位置を確認する：

##### 1. ヘッダー行の動的確認

```
1. Excelファイルを開く
2. ヘッダー行（通常1-5行目）を読み取り
3. 「必須」「項目名」「データ型」「桁数」などの列名を検索
4. 各列の位置を動的に特定・記録
```

##### 2. 列位置の特定方法

```
- 「必須」列：「必須」「必要」などのキーワードで検索
- 「項目名」列：「項目名」「フィールド名」などのキーワードで検索
- 「データ型」列：「データ型」「型」などのキーワードで検索
- 固定の列番号（Q列、S列など）に依存しない
```

##### 3. データレコード部分の特定

```
1. 「②データレコード」「データレコード」などの見出しを検索
2. その後に続く項目一覧を特定
3. 項目番号が1から始まる部分を確認
4. 動的に特定した列位置を使用して項目を確認
```

#### 確認・修正手順

##### Step 1: ファイル構造の動的確認

```
1. read_data_from_excel でヘッダー部分を読み取り
2. 各セルの値から「必須」「項目名」などのキーワードを検索
3. 該当する列番号を変数に保存
4. ファイル固有の列構造を把握
```

##### Step 2: 対象項目の検索と確認

```
1. データレコード部分で対象項目名を検索
2. 動的に特定した「必須」列で現在の値を確認
3. 必要に応じて「○」「-」を設定
4. 修正箇所を赤色でマーキング
```

##### Step 3: 検証

```
1. 修正後の値を再確認
2. 他の関連項目への影響を確認
3. ファイル保存前の最終チェック
```

#### 注意点

##### フォーマット相違への対応

- **固定列番号禁止**: Q 列、S 列などの固定番号に依存しない
- **動的確認必須**: 必ずヘッダー行から列位置を動的に特定
- **ファイル個別対応**: ファイルごとに構造が異なる可能性を考慮

##### エラー回避

- **項目名検索**: 完全一致で検索、表記揺れ（全角/半角、スペース）に注意
- **列位置確認**: 「必須」列が見つからない場合は手動でヘッダー構造を確認
- **データレコード特定**: ヘッダー部分とデータレコード部分を混同しない

##### 実装例

```
# 動的列位置特定のサンプル手順
1. read_data_from_excel でヘッダー行（1-5行目）を読み取り
2. 各セルの値から「必須」キーワードを検索
3. 該当する列番号（例：I列=9、Q列=17、S列=19）を変数に保存
4. データレコード部分で、保存した列番号を使用して確認・修正
```

この動的確認ルールにより、デ連側・DWH 側のフォーマット相違に関係なく、柔軟に対応可能。

## 🚨 よくある操作ミスと防止策

### ❌ よくある操作ミス

1. **ヘッダーレコード部分を修正してしまう**

   - 行 19-35（ヘッダーレコード部分）で必須列を修正
   - 正しくは行 37 以降（データレコード部分）で修正

2. **項番と行番号の混同**

   - 項番 3 だから行 3 で修正
   - 正しくは項番 3 = データレコード部分の行 39

3. **列位置の固定思考**
   - 常に Q 列が必須列だと思い込む
   - ファイルによって S 列の場合もある

### ✅ 操作ミス防止策

1. **必ず「② データレコード」を確認**

   ```
   - 行36に「②データレコード」の見出しがあることを確認
   - その後の行（37以降）で修正作業を実施
   ```

2. **項番から行番号への変換ルール**

   ```
   データレコード部分の行番号 = 36 + 項番
   例：項番3 → 行39（36+3）
       項番5 → 行41（36+5）
   ```

3. **修正前後の値確認**
   ```
   - 修正前：現在の値を確認
   - 修正後：期待する値に変更されているか確認
   - 赤色マーキング：修正箇所が視覚的に分かるか確認
   ```

## 📋 修正作業チェックリスト

### 修正前チェック

- [ ] ファイル構造を確認（① ヘッダーレコード、② データレコード）
- [ ] 列位置を動的確認（P 列：一意、Q 列：必須）
- [ ] 対象項目をデータレコード部分で特定
- [ ] 修正前の値を確認

### 修正実施チェック

- [ ] データレコード部分（行 37 以降）で修正
- [ ] 正しい列（必須列・一意列）で修正
- [ ] 正しい値（「○」「-」）に変更
- [ ] 赤色マーキングを実施

### 修正後チェック

- [ ] 修正後の値を確認
- [ ] 赤色マーキングが適用されているか確認
- [ ] 変更履歴シートを更新
- [ ] 他の項目に影響がないか確認
