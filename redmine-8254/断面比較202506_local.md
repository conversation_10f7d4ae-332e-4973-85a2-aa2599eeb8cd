# 断面比較202506_local

## 担当・IF定義書修正一覧

| 担当 | IF定義書修正 | entityname | columnname | MDM/OMS | デ連IF | ifname | columnname | DWHDB | ifid | columnname | 要否判断 | 対応内容 |
|------|-------------|------------|------------|---------|--------|--------|------------|-------|------|------------|----------|----------|
| 済 | カテゴリ | スマートフォン用カテゴリ名称 | カテゴリ | スマートフォン用カテゴリ名称 | カテゴリ | スマートフォン用カテゴリ名称 | 要 | 必須追加が必要 | 黄 |
| 済 | クーポン | クーポンマスタ | クーポン | クーポンマスタ | クーポン | クーポン商品シリーズ | 要 | 項目削除が必要 | 黄 |
| 済 | クーポン | クーポン商品シリーズ | クーポン | クーポン大分類 | クーポン | クーポン大分類 | 要 | 項目削除が必要 | 黄 |
| 済 | クーポン | クーポン大分類 | クーポン | クーポン自動発行種別 | クーポン | クーポン自動発行種別 | 要 | 必須設定が必要 | 黄 |
| 済 | クーポン | クーポン自動発行種別 | クーポン | クーポンマスタ | クーポン | クーポン部門 | 要 | 項目削除が必要 | 黄 |
| 済 | クーポン | クーポンマスタ | クーポン | クーポン自動発行種別 | クーポン | 販売経路指定 | 要 | 項目削除が必要 | 黄 |
| 済 | 出荷ヘッダ | 会員ステージ | 出荷ヘッダ | 会員ステージ | 出荷ヘッダ | 会員ステージ | 要 | 必須設定が必要 | 黄 |
| 済 | 出荷ヘッダ | 住所3 | 出荷ヘッダ | 住所3 | 出荷ヘッダ | 住所3 | 要 | デ連IFのみ桁数修正が必要 | 黄 |
| 済 | 出荷ヘッダ | 佐川集荷日 | 出荷ヘッダ | 宛名：名 | 出荷ヘッダ | 宛名：名 | 要 | 項目追加が必要 | 黄 |
| 済 | 出荷ヘッダ | 受取店舗ID | 出荷ヘッダ | 宛名名かな | 出荷ヘッダ | 宛名名かな | 要 | 項目追加が必要 | 黄 |
| 済 | 出荷ヘッダ | 受取店舗区分 | 出荷ヘッダ | 送料消費税額 | 出荷ヘッダ | 送料消費税額 | 要 | 項目追加が必要 | 黄 |
| 済 | 出荷ヘッダ | 受取店舗名称 | 出荷ヘッダ | 配送情報番号 | 出荷明細構成品 | 商品名称 | 要 | 項目追加が必要 | 黄 |
| 済 | 出荷ヘッダ | 宛名：名 | 出荷明細構成品 | 商品名称 | 出荷明細構成品 | 商品消費税額 | 要 | デ連IFのみ必須解除が必要 | 黄 |
| 済 | 出荷ヘッダ | 宛名名かな | 出荷明細構成品 | 商品消費税額 | 出荷明細構成品 | 販売時消費税額 | 要 | デ連IFのみ必須解除が必要 | 黄 |
| 済 | 出荷ヘッダ | 送料消費税額 | 出荷明細構成品 | 販売時消費税額 | 受注キャンペーン適用 | キャンペーン適用終了日 | 要 | 桁数修正が必要 | 黄 |
| 済 | 出荷ヘッダ | 配送情報番号 | 受注キャンペーン適用 | キャンペーン適用終了日 | 受注キャンペーン適用 | キャンペーン適用終了日 | 要 | 項目追加が必要 | 黄 |
| 済 | 出荷明細構成品 | 商品名称 | 受注キャンペーン適用 | キャンペーン適用終了日 | 受注キャンペーン適用履歴 | キャンペーン適用終了日 | 要 | デ連IFのみ桁数修正が必要 | 黄 |
| 済 | 出荷明細構成品 | 商品消費税額 | 受注キャンペーン適用履歴 | キャンペーン適用終了日 | 受注キャンペーン適用履歴 | キャンペーン適用終了日 | 要 | 桁数修正が必要 | 黄 |
| 済 | 出荷明細構成品 | 販売時消費税額 | 受注キャンペーン適用履歴 | キャンペーン適用終了日 | 受注キャンペーン適用履歴 | キャンペーン適用終了日 | 要 | 桁数修正が必要 | 黄 |

## 商品セグメントマスタ関連

| entityname | columnname | ifname | columnname | ifid | columnname | 要否判断 | 対応内容 | 担当 |
|------------|------------|--------|------------|------|------------|----------|----------|------|
| 商品セグメントマスタ | 更新クラス | 商品セグメントマスタ | 更新クラス | 商品セグメントマスタ | 更新クラス | 要 | デ連IFのみ桁数修正が必要? | 黄 |
| 商品セグメントマスタ | 更新日 | 商品セグメントマスタ | 更新日 | 商品セグメントマスタ | 更新日 | 要 | デ連IFのみ桁数修正が必要? | 黄 |
| 商品セグメントマスタ | 更新者ID | 商品セグメントマスタ | 更新者ID | 商品セグメントマスタ | 更新者ID | 要 | 項目追加が必要 | 黄 |
| 商品セグメントマスタ | 登録クラス | 商品セグメントマスタ | 登録クラス | 商品セグメントマスタ | 登録クラス | 要 | 項目追加が必要 | 黄 |
| 商品セグメントマスタ | 登録日 | 商品セグメントマスタ | 登録日 | 商品セグメントマスタ | 登録日 | 要 | 項目追加が必要 | 黄 |
| 商品セグメントマスタ | 登録者ID | 商品セグメントマスタ | 登録者ID | 商品セグメントマスタ | 登録者ID | 要 | 項目追加が必要 | 黄 |

## 商品種別マスタ関連

| entityname | columnname | ifname | columnname | ifid | columnname | 要否判断 | 対応内容 | 担当 |
|------------|------------|--------|------------|------|------------|----------|----------|------|
| 商品種別マスタ | 更新クラス | 商品種別マスタ | 更新クラス | 商品種別マスタ | 更新クラス | 要 | 項目追加が必要 | 黄 |
| 商品種別マスタ | 更新日 | 商品種別マスタ | 更新日 | 商品種別マスタ | 更新日 | 要 | 項目追加が必要 | 黄 |
| 商品種別マスタ | 更新者ID | 商品種別マスタ | 更新者ID | 商品種別マスタ | 更新者ID | 要 | 項目追加が必要 | 黄 |
| 商品種別マスタ | 登録クラス | 商品種別マスタ | 登録クラス | 商品種別マスタ | 登録クラス | 要 | 項目追加が必要 | 黄 |
| 商品種別マスタ | 登録日 | 商品種別マスタ | 登録日 | 商品種別マスタ | 登録日 | 要 | 項目追加が必要 | 黄 |
| 商品種別マスタ | 登録者ID | 商品種別マスタ | 登録者ID | 商品種別マスタ | 登録者ID | 要 | 項目追加が必要 | 黄 |

## 在庫・売上実績関連

| entityname | columnname | ifname | columnname | ifid | columnname | 要否判断 | 対応内容 | 担当 |
|------------|------------|--------|------------|------|------------|----------|----------|------|
| 在庫 | 予約数量 | 在庫 | 予約数量 | 在庫 | 予約数量 | 要 | 項目追加が必要 | 黄 |
| 売上実績 | 入力番号 | 在庫 | 予約数量 | 在庫 | 予約数量 | 要 | 必須設定が必要 | 黄 |

## 大分類マスタ関連

| entityname | columnname | ifname | columnname | ifid | columnname | 要否判断 | 対応内容 | 担当 |
|------------|------------|--------|------------|------|------------|----------|----------|------|
| 大分類マスタ | 更新クラス | 大分類マスタ | 更新クラス | 大分類マスタ | 更新クラス | 要 | 項目追加が必要 | 黄 |
| 大分類マスタ | 更新日 | 大分類マスタ | 更新日 | 大分類マスタ | 更新日 | 要 | 項目追加が必要 | 黄 |
| 大分類マスタ | 更新者ID | 大分類マスタ | 更新者ID | 大分類マスタ | 更新者ID | 要 | 項目追加が必要 | 黄 |
| 大分類マスタ | 登録クラス | 大分類マスタ | 登録クラス | 大分類マスタ | 登録クラス | 要 | 項目追加が必要 | 黄 |
| 大分類マスタ | 登録日 | 大分類マスタ | 登録日 | 大分類マスタ | 登録日 | 要 | 項目追加が必要 | 黄 |
| 大分類マスタ | 登録者ID | 大分類マスタ | 登録者ID | 大分類マスタ | 登録者ID | 要 | 項目追加が必要 | 黄 |

## 定期契約関連

| entityname | columnname | ifname | columnname | ifid | columnname | 要否判断 | 対応内容 | 担当 |
|------------|------------|--------|------------|------|------------|----------|----------|------|
| 定期契約ヘッダ | デバイス区分 | 定期契約ヘッダ | 外部受注番号 | 定期契約ヘッダ | 外部受注番号 | 要 | デ連IFのみ桁数修正が必要 | 黄 |
| 定期契約ヘッダ | 外部受注番号 | 定期契約明細構成品 | 商品名称 | 定期契約明細構成品 | 商品名称 | 要 | デ連IFのみ桁数修正が必要 | 黄 |
| 定期契約明細構成品 | 商品名称 | 定期契約ヘッダ | 外部受注番号 | 定期契約ヘッダ | 外部受注番号 | 要 | 項目追加が必要 | 黄 |

## 期間別価格連携マスタ関連

| entityname | columnname | ifname | columnname | ifid | columnname | 要否判断 | 対応内容 | 担当 |
|------------|------------|--------|------------|------|------------|----------|----------|------|
| 期間別価格連携マスタ | 更新クラス | 期間別価格連携マスタ | 更新クラス | 期間別価格連携マスタ | 更新クラス | 要 | 項目追加が必要 | 黄 |
| 期間別価格連携マスタ | 更新日 | 期間別価格連携マスタ | 更新日 | 期間別価格連携マスタ | 更新日 | 要 | 項目追加が必要 | 黄 |
| 期間別価格連携マスタ | 更新者ID | 期間別価格連携マスタ | 更新者ID | 期間別価格連携マスタ | 更新者ID | 要 | 項目追加が必要 | 黄 |
| 期間別価格連携マスタ | 登録クラス | 期間別価格連携マスタ | 登録クラス | 期間別価格連携マスタ | 登録クラス | 要 | 項目追加が必要 | 黄 |
| 期間別価格連携マスタ | 登録日 | 期間別価格連携マスタ | 登録日 | 期間別価格連携マスタ | 登録日 | 要 | 項目追加が必要 | 黄 |
| 期間別価格連携マスタ | 登録者ID | 期間別価格連携マスタ | 登録者ID | 期間別価格連携マスタ | 登録者ID | 要 | 項目追加が必要 | 黄 |
| 期間別価格連携マスタ | 適用開始日 | 期間別価格連携マスタ | 適用開始日 | 期間別価格連携マスタ | 適用開始日 | 要 | デ連IFのみ桁数修正が必要? | 黄 |

## 返品ヘッダ関連

| entityname | columnname | ifname | columnname | ifid | columnname | 要否判断 | 対応内容 | 担当 |
|------------|------------|--------|------------|------|------------|----------|----------|------|
| 返品ヘッダ | キャンペーン返品額 | 返品ヘッダ | キャンペーン返品額 | 返品ヘッダ | キャンペーン返品額 | 要 | 項目追加が必要 | 黄 |
| 返品ヘッダ | クーポン返品額 | 返品ヘッダ | クーポン返品額 | 返品ヘッダ | クーポン返品額 | 要 | 項目追加が必要 | 黄 |
| 返品ヘッダ | キャンペーン判定基準日時 | 返品ヘッダ | キャンペーン判定基準日時 | 返品ヘッダ | キャンペーン判定基準日時 | 要 | 項目追加が必要 | 黄 |
| 返品ヘッダ | 受注番号 | 返品ヘッダ | 受注番号 | 返品ヘッダ | 受注番号 | 要 | デ連IFのみユニーク解除が必要 | 黄 |
| 返品ヘッダ | 返品前クーポン利用額 | 返品ヘッダ | 返品前クーポン利用額 | 返品ヘッダ | 返品前クーポン利用額 | 要 | 項目追加が必要 | 黄 |
| 返品ヘッダ | 返品後クーポン利用額 | 返品ヘッダ | 返品後クーポン利用額 | 返品ヘッダ | 返品後クーポン利用額 | 要 | 項目追加が必要 | 黄 |

## 削除項目一覧

以下の項目は削除が必要とされています：

- 商品セグメントマスタ（更新日、更新者ID、登録日、登録者ID）
- 商品種別マスタ（更新日、更新者ID、登録日、登録者ID）
- 大分類マスタ（更新日、更新者ID、登録日、登録者ID）
- 期間別価格連携マスタ（更新日、更新者ID、登録日、登録者ID）

すべての項目の担当者は「黄」となっています。
