import json
import boto3
import os
import re
import urllib
from botocore.config import Config
import time

def lambda_handler(event, context):
    try:
        required_params = [
                "dir_path",
                "file_name",
                "backup_dir",
            ]
        # パラメータ取得
        dir_path = event.get("dir_path")
        file_name = event.get("file_name")
        backup_dir = event.get("backup_dir")

        retry_limit = int(os.environ.get("retry_limit"))

        # 環境情報の取得
        params = get_parameter_from_parameter_store("/glue/job/environment-config", retry_limit)
        params = json.loads(params)["aws"]
        bucket_name = params["S3_BUCKET_NAME"]
    
        for require_param in required_params:
            if eval(require_param) is None:
                print(f"必須のパラメータが設定されていません。　パラメータ名:{require_param}")
                raise ValueError(f"Required parameter '{require_param}' is missing")

        print(f"ファイル配置チェックを開始　ディレクトリパス:{dir_path} ファイル名:{file_name}　バックアップ:{backup_dir}")
        
        # Initialize S3 client
        s3_client = get_s3_client(retry_limit)
        
        # ディレクトリ一覧の取得
        sorted_prefixes = find_s3_prefix(dir_path, bucket_name, s3_client)
    
        # ファイル配置チェック
        if len(sorted_prefixes) == 0:
            # ディレクトリが存在しない場合
            print(f"ファイルが未配置のため、処理終了。チェックしたディレクトリ:{dir_path}")
            is_execute = False
        elif len(sorted_prefixes) == 1:
            # 存在するディレクトリが１個の場合
            is_file_exist, files = find_s3_file(sorted_prefixes[0], file_name, bucket_name, s3_client)
            if is_file_exist:
                print(f"ファイルが存在。チェックしたディレクトリ:{sorted_prefixes[0]}")
                is_execute = True
            else:
                print(f"ファイルが未配置のため、処理終了。チェックしたディレクトリ:{sorted_prefixes[0]}")
                is_execute = False
        else:
            # 存在するディレクトリが複数(2個以上)の場合
            is_file_exist, files = find_s3_file(sorted_prefixes[0], file_name, bucket_name, s3_client)
            if is_file_exist:
                # 配置チェック対象のファイルが存在する場合
                print(f"ディレクトリが複数あり、最過去のディレクトリにファイルが存在。最過去ディレクトリ:{sorted_prefixes[0]}　次ディレクトリ:{sorted_prefixes[1]}")
                is_execute = True
            else:
                # 配置チェック対象のファイルが存在しない場合
                print(f"ディレクトリが複数あり、最過去のディレクトリにファイルが未配置。最過去ディレクトリ:{sorted_prefixes[0]}　次ディレクトリ:{sorted_prefixes[1]}")
                # バックアップ退避（例外発生）
                for file in files:
                    prefix_parts = sorted_prefixes[0].rstrip('/').split('/')
                    backup_input_file(bucket_name, file, os.path.join(backup_dir,prefix_parts[-1]), os.path.basename(file), s3_client)
                    delete_input_file(bucket_name, file, s3_client)
                raise ValueError(f"several directories exist but target file not exists.")
        result = {"is_execute":is_execute}
        return result
    except Exception as e:
        raise e

def find_s3_prefix(aws_s3_dir: str, bucket_name: str, s3_client):
    """
    指定されたS3ディレクトリ（プレフィックス）に基づき、日付サフィックス付きのプレフィックスを検索します。
    例: 'a/b/' -> ('a/b_20240321/', '20240321') or ('a/b/', '')

    Args:
        aws_s3_dir (str): S3バケット内のディレクトリ（プレフィックス）。バケット名やファイル名は含まれません。
        bucket_name (str): 対象のS3バケットの名前。
        s3_client: s3クライアント
    Returns:
        List: 条件に合うディレクトリ一覧リスト　条件に合うディレクトリがない場合空リストを返却する
    """
    # Remove trailing slash if exists
    aws_s3_dir_prefix = aws_s3_dir.rstrip('/').rstrip('*')

    # List all objects that match the prefix aws_s3_dir
    response = s3_client.list_objects_v2(
        Bucket=bucket_name,
        Prefix=aws_s3_dir_prefix,
        Delimiter='/'
    )

    # Extract the prefix matches from the response
    if 'CommonPrefixes' not in response or not response['CommonPrefixes']:
        return []

    prefixes = [prefix['Prefix'] for prefix in response['CommonPrefixes']]

    # Sort the prefixes in ascending order
    sorted_prefixes = sorted(prefixes)

    return sorted_prefixes

def find_s3_file(prefix, file_name, bucket_name: str, s3_client):
    """
    指定されたS3のパス、ファイル名に基づき、もっとも古い日付付きのファイル名を検索します。
    例: 'filename*.ext' -> 'filename20240321.ext'

    Args:
        aws_s3_dir (str): S3バケット内のディレクトリ（プレフィックス）。バケット名やファイル名は含まれません。
        file_name (str): S3バケット内のファイル名(ファイル名には*を含む）
        bucket_name (str): 対象のS3バケットの名前。
        s3_client: s3クライアント
    Returns:
        boolean: True:対象ファイルが存在 False:存在しない
        List: aws_s3_dirディレクトリ配下のファイル一覧
    """

    # List all objects that match the prefix aws_s3_dir
    response = s3_client.list_objects_v2(
        Bucket=bucket_name,
        Prefix=prefix,
        Delimiter='/'
    )
    
    # 指定したディレクトリ直下のファイルを取得
    files = [
        obj['Key'] for obj in response.get('Contents', [])
        if obj['Key'].startswith(prefix) and '/' not in obj['Key'][len(prefix):]
    ]

    # ファイル名だけを抽出
    file_names = [os.path.basename(file) for file in files]
    
    # 取得するファイルパターンの決定
    escape_file_name = file_name.replace(".", "\.")  # "."をエスケープするための処理
    file_pattern = re.compile(fr'{escape_file_name.replace("*", ".*")}', re.IGNORECASE)  # "*"を".*"に変換してパターン化

    targets = []
    for file in file_names:
        if file_pattern.match(file):
            targets.append(file)
    else:
        if not targets:
            return False, files
    return True, files

def backup_input_file(
        bucket_name: str, s3_storage_file_full_path: str, backup_file_dir: str, file_name: str, s3_client
    ):
    """
    インプットファイルバックアップ
    Args:
        s3_storage_file_full_path: S3ストレージファイルパス
        backup_file_dir: バックアップファイルディレクトリ
        file_name: 連携元のファイルパス[ファイル名]
    """
    s3_client.copy_object(
        Bucket=bucket_name,
        Key=os.path.join(backup_file_dir ,file_name),
        CopySource={"Bucket": bucket_name, "Key": s3_storage_file_full_path},
    )


def delete_input_file(bucket_name: str, s3_full_path: str, s3_client):
    """
    インプットファイル削除
    Args:
        s3_full_path: S3フルパス
    """

    s3_client.delete_object(
        Bucket=bucket_name, Key=s3_full_path
    )


def get_parameter_from_parameter_store(param_name, retry_limit):
    retry_times = retry_limit
    for i in range(retry_times):
        try:
            aws_session_token = os.environ.get("AWS_SESSION_TOKEN")
            req = urllib.request.Request(
                "http://localhost:2773/systemsmanager"
                + f"/parameters/get/?name={param_name}")
            req.add_header("X-Aws-Parameters-Secrets-Token", aws_session_token)
            config = urllib.request.urlopen(req).read()
            param = json.loads(config)["Parameter"]["Value"]
            return param
        except Exception as e:
            if i == retry_times - 1:
                print(f"Parameter Storeからの値取得に失敗しました。　設定値:{param_name}")
                raise e
            time.sleep(1)

def create_s3_client(
    region_name: str = None, max_attempts: int = 3, mode: str = "standard"
) -> boto3.client:
    config = Config(retries={"max_attempts": max_attempts, "mode": mode})

    return boto3.client("s3", region_name=region_name, config=config)


def get_s3_client(retry_limit):
    region_name = "ap-northeast-1"
    return create_s3_client(
        region_name=region_name, max_attempts=retry_limit
    )